/**
 * @file AuthorizationManager.h
 * @brief Authorization and permission management for QuantServices-SaaS
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include <memory>
#include <shared_mutex>
#include <regex>

#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

using Json = nlohmann::json;

namespace QuantServicesSaaS {

/**
 * @brief Permission structure
 */
struct Permission {
    std::string name;
    std::string resource;
    std::string action;
    std::string description;
    Json metadata;
    
    // Parse permission string like "market_data:read" or "trading:orders:write"
    static Permission from_string(const std::string& permission_str);
    std::string to_string() const;
    bool matches(const std::string& resource, const std::string& action) const;
};

/**
 * @brief Role structure
 */
struct Role {
    std::string name;
    std::string description;
    std::vector<Permission> permissions;
    std::unordered_set<std::string> inherited_roles;
    bool is_system_role{false};
    Json metadata;
    
    bool has_permission(const std::string& resource, const std::string& action) const;
    bool has_permission(const Permission& permission) const;
};

/**
 * @brief Resource access control entry
 */
struct AccessControlEntry {
    std::string user_id;
    std::string resource_type;
    std::string resource_id;
    std::vector<std::string> allowed_actions;
    std::vector<std::string> denied_actions;
    Json conditions; // Additional conditions for access
    std::chrono::system_clock::time_point expires_at;
    bool is_active{true};
};

/**
 * @brief Authorization context for permission checks
 */
struct AuthorizationContext {
    std::string user_id;
    std::string session_id;
    std::vector<std::string> user_roles;
    std::string remote_address;
    std::string user_agent;
    Json request_data;
    std::chrono::system_clock::time_point timestamp;
};

/**
 * @brief Authorization result
 */
struct AuthorizationResult {
    bool allowed{false};
    std::string reason;
    std::vector<std::string> required_permissions;
    std::vector<std::string> missing_permissions;
    Json metadata;
};

/**
 * @brief Authorization configuration
 */
struct AuthorizationConfig {
    bool enable_rbac{true};
    bool enable_abac{false}; // Attribute-Based Access Control
    bool strict_mode{true};
    std::string default_role{"user"};
    std::vector<std::string> admin_roles{"admin"};
    std::vector<std::string> system_roles{"admin", "trader", "viewer", "user"};
    bool enable_resource_acl{false};
    bool enable_audit_logging{true};
};

/**
 * @brief Authorization Manager class
 */
class AuthorizationManager {
public:
    /**
     * @brief Constructor
     */
    explicit AuthorizationManager(const Json& config);
    
    /**
     * @brief Destructor
     */
    ~AuthorizationManager();
    
    /**
     * @brief Initialize authorization manager
     */
    bool initialize();
    
    // Role management
    bool create_role(const Role& role);
    bool update_role(const std::string& role_name, const Role& role);
    bool delete_role(const std::string& role_name);
    std::optional<Role> get_role(const std::string& role_name) const;
    std::vector<Role> get_all_roles() const;
    
    // Permission management
    bool create_permission(const Permission& permission);
    bool delete_permission(const std::string& permission_name);
    std::optional<Permission> get_permission(const std::string& permission_name) const;
    std::vector<Permission> get_all_permissions() const;
    
    // User role assignment
    bool assign_role_to_user(const std::string& user_id, const std::string& role_name);
    bool remove_role_from_user(const std::string& user_id, const std::string& role_name);
    std::vector<std::string> get_user_roles(const std::string& user_id) const;
    std::vector<Permission> get_user_permissions(const std::string& user_id) const;
    
    // Resource-based access control
    bool grant_resource_access(const AccessControlEntry& ace);
    bool revoke_resource_access(const std::string& user_id, const std::string& resource_type, 
                               const std::string& resource_id);
    std::vector<AccessControlEntry> get_user_resource_access(const std::string& user_id) const;
    
    // Authorization checks
    AuthorizationResult check_permission(const AuthorizationContext& context, 
                                       const std::string& resource, 
                                       const std::string& action) const;
    
    AuthorizationResult check_permissions(const AuthorizationContext& context,
                                        const std::vector<std::string>& required_permissions) const;
    
    bool has_permission(const std::string& user_id, const std::string& resource, 
                       const std::string& action) const;
    
    bool has_role(const std::string& user_id, const std::string& role_name) const;
    
    bool is_admin(const std::string& user_id) const;
    
    // Resource access checks
    bool can_access_resource(const std::string& user_id, const std::string& resource_type,
                           const std::string& resource_id, const std::string& action) const;
    
    // Utility methods
    std::vector<std::string> expand_permissions(const std::vector<std::string>& permissions) const;
    std::vector<Permission> resolve_role_permissions(const std::string& role_name) const;
    bool validate_permission_format(const std::string& permission) const;
    
    // Configuration management
    bool load_roles_from_config(const Json& roles_config);
    bool load_permissions_from_config(const Json& permissions_config);
    Json export_configuration() const;
    
    // Audit and logging
    void log_authorization_event(const AuthorizationContext& context, 
                               const std::string& resource, 
                               const std::string& action,
                               const AuthorizationResult& result) const;
    
    // Statistics
    Json get_authorization_stats() const;

private:
    // Configuration
    Json config_;
    AuthorizationConfig authz_config_;
    
    // Logger
    std::shared_ptr<spdlog::logger> logger_;
    
    // Role and permission storage
    mutable std::shared_mutex roles_mutex_;
    std::unordered_map<std::string, Role> roles_;
    
    mutable std::shared_mutex permissions_mutex_;
    std::unordered_map<std::string, Permission> permissions_;
    
    // User role assignments
    mutable std::shared_mutex user_roles_mutex_;
    std::unordered_map<std::string, std::unordered_set<std::string>> user_roles_; // user_id -> role_names
    
    // Resource access control
    mutable std::shared_mutex resource_acl_mutex_;
    std::vector<AccessControlEntry> resource_acl_;
    std::unordered_map<std::string, std::vector<size_t>> user_resource_index_; // user_id -> ACE indices
    
    // Permission cache
    mutable std::shared_mutex permission_cache_mutex_;
    mutable std::unordered_map<std::string, std::vector<Permission>> user_permission_cache_; // user_id -> permissions
    mutable std::chrono::steady_clock::time_point cache_last_update_;
    std::chrono::seconds cache_ttl_{300}; // 5 minutes
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::atomic<uint64_t> total_checks_{0};
    std::atomic<uint64_t> allowed_checks_{0};
    std::atomic<uint64_t> denied_checks_{0};
    std::atomic<uint64_t> cache_hits_{0};
    std::atomic<uint64_t> cache_misses_{0};
    
    // Internal methods
    void load_config(const Json& config);
    void setup_default_roles();
    void setup_default_permissions();
    
    bool validate_role(const Role& role) const;
    bool validate_permission(const Permission& permission) const;
    
    std::vector<Permission> get_cached_user_permissions(const std::string& user_id) const;
    void update_permission_cache(const std::string& user_id, const std::vector<Permission>& permissions) const;
    void invalidate_permission_cache(const std::string& user_id) const;
    void cleanup_expired_cache() const;
    
    bool check_resource_acl(const std::string& user_id, const std::string& resource_type,
                          const std::string& resource_id, const std::string& action) const;
    
    bool evaluate_conditions(const Json& conditions, const AuthorizationContext& context) const;
    
    void update_stats(bool allowed) const;
};

} // namespace QuantServicesSaaS
