/**
 * @file WebSocketServer.cpp
 * @brief WebSocket server implementation for real-time data communication
 * <AUTHOR> Team
 * @date 2024
 */

#include "WebSocketServer.h"
#include "AuthenticationManager.h"
#include "QuantServicesProxy.h"
#include <spdlog/spdlog.h>
#include <random>
#include <iomanip>
#include <sstream>

namespace QuantServicesSaaS {

// WebSocketConnection Implementation
WebSocketConnection::WebSocketConnection(std::string id, std::unique_ptr<WebSocketStream> ws)
    : id_(std::move(id))
    , ws_(std::move(ws))
    , is_ssl_(false)
    , last_activity_(std::chrono::steady_clock::now()) {
}

WebSocketConnection::WebSocketConnection(std::string id, std::unique_ptr<SSLWebSocketStream> ssl_ws)
    : id_(std::move(id))
    , ssl_ws_(std::move(ssl_ws))
    , is_ssl_(true)
    , last_activity_(std::chrono::steady_clock::now()) {
}

WebSocketConnection::~WebSocketConnection() {
    close();
}

void WebSocketConnection::send_message(const Json& message) {
    std::lock_guard<std::mutex> lock(send_mutex_);
    send_queue_.push(message);
    
    if (!sending_.exchange(true)) {
        process_send_queue();
    }
}

void WebSocketConnection::process_send_queue() {
    while (true) {
        Json message;
        {
            std::lock_guard<std::mutex> lock(send_mutex_);
            if (send_queue_.empty()) {
                sending_ = false;
                return;
            }
            message = send_queue_.front();
            send_queue_.pop();
        }
        
        try {
            std::string message_str = message.dump();
            
            if (is_ssl_ && ssl_ws_) {
                ssl_ws_->write(net::buffer(message_str));
            } else if (!is_ssl_ && ws_) {
                ws_->write(net::buffer(message_str));
            }
            
            update_activity();
        } catch (const std::exception& e) {
            spdlog::error("Failed to send WebSocket message: {}", e.what());
            break;
        }
    }
    
    sending_ = false;
}

void WebSocketConnection::close() {
    try {
        if (is_ssl_ && ssl_ws_) {
            ssl_ws_->close(websocket::close_code::normal);
        } else if (!is_ssl_ && ws_) {
            ws_->close(websocket::close_code::normal);
        }
    } catch (const std::exception& e) {
        spdlog::debug("Error closing WebSocket connection: {}", e.what());
    }
}

bool WebSocketConnection::is_open() const {
    try {
        if (is_ssl_ && ssl_ws_) {
            return ssl_ws_->is_open();
        } else if (!is_ssl_ && ws_) {
            return ws_->is_open();
        }
    } catch (const std::exception&) {
        return false;
    }
    return false;
}

// WebSocketServer Implementation
WebSocketServer::WebSocketServer(const Json& config,
                               std::shared_ptr<AuthenticationManager> auth_manager,
                               std::shared_ptr<QuantServicesProxy> qs_proxy)
    : config_(config)
    , auth_manager_(auth_manager)
    , qs_proxy_(qs_proxy)
    , logger_(spdlog::get("saas-backend"))
    , start_time_(std::chrono::steady_clock::now()) {
    
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    load_config(config);
}

WebSocketServer::~WebSocketServer() {
    if (running_.load()) {
        auto stop_future = stop();
        stop_future.wait();
    }
}

void WebSocketServer::load_config(const Json& config) {
    auto ws_config = config.value("websocket", Json::object());
    
    ws_config_.host = ws_config.value("host", "0.0.0.0");
    ws_config_.port = ws_config.value("port", 9081);
    ws_config_.worker_threads = ws_config.value("worker_threads", 4);
    ws_config_.connection_timeout = std::chrono::seconds(ws_config.value("connection_timeout_seconds", 300));
    ws_config_.heartbeat_interval = std::chrono::seconds(ws_config.value("heartbeat_interval_seconds", 30));
    ws_config_.max_connections = ws_config.value("max_connections", 1000);
    ws_config_.message_queue_size = ws_config.value("message_queue_size", 10000);
    ws_config_.enable_ssl = ws_config.value("enable_ssl", false);
    ws_config_.ssl_cert_file = ws_config.value("ssl_cert_file", "");
    ws_config_.ssl_key_file = ws_config.value("ssl_key_file", "");
}

bool WebSocketServer::initialize() {
    try {
        logger_->info("Initializing WebSocket server...");
        
        if (ws_config_.enable_ssl) {
            setup_ssl_context();
        }
        
        initialized_ = true;
        logger_->info("WebSocket server initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize WebSocket server: {}", e.what());
        return false;
    }
}

void WebSocketServer::setup_ssl_context() {
    ssl_context_ = std::make_unique<net::ssl::context>(net::ssl::context::tlsv12);
    
    ssl_context_->set_options(
        net::ssl::context::default_workarounds |
        net::ssl::context::no_sslv2 |
        net::ssl::context::no_sslv3 |
        net::ssl::context::single_dh_use);
    
    if (!ws_config_.ssl_cert_file.empty()) {
        ssl_context_->use_certificate_chain_file(ws_config_.ssl_cert_file);
    }
    
    if (!ws_config_.ssl_key_file.empty()) {
        ssl_context_->use_private_key_file(ws_config_.ssl_key_file, net::ssl::context::pem);
    }
}

std::future<bool> WebSocketServer::start() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            if (running_.load()) {
                logger_->warn("WebSocket server is already running");
                return true;
            }
            
            if (!initialized_) {
                logger_->error("WebSocket server not initialized");
                return false;
            }
            
            logger_->info("Starting WebSocket server on {}:{}", ws_config_.host, ws_config_.port);
            
            // Create acceptor
            auto const address = net::ip::make_address(ws_config_.host);
            acceptor_ = std::make_unique<tcp::acceptor>(io_context_, tcp::endpoint{address, ws_config_.port});
            
            running_.store(true);
            
            // Start worker threads
            worker_threads_.reserve(ws_config_.worker_threads);
            for (size_t i = 0; i < ws_config_.worker_threads; ++i) {
                worker_threads_.emplace_back(&WebSocketServer::run_worker, this);
            }
            
            // Start message processor
            message_processing_ = true;
            message_processor_thread_ = std::thread(&WebSocketServer::process_message_queue, this);
            
            // Start heartbeat thread
            heartbeat_running_ = true;
            heartbeat_thread_ = std::thread(&WebSocketServer::send_heartbeat, this);
            
            // Setup market data subscriptions
            setup_market_data_subscriptions();
            
            // Start accepting connections
            handle_accept();
            
            logger_->info("WebSocket server started successfully");
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Failed to start WebSocket server: {}", e.what());
            return false;
        }
    });
}

std::future<bool> WebSocketServer::stop() {
    return std::async(std::launch::async, [this]() -> bool {
        try {
            logger_->info("Stopping WebSocket server...");
            
            running_.store(false);
            
            // Stop accepting new connections
            if (acceptor_) {
                acceptor_->close();
            }
            
            // Close all connections
            {
                std::unique_lock<std::shared_mutex> lock(connections_mutex_);
                for (auto& [id, connection] : connections_) {
                    connection->close();
                }
                connections_.clear();
                user_connections_.clear();
                topic_subscribers_.clear();
            }
            
            // Stop message processing
            message_processing_ = false;
            message_queue_cv_.notify_all();
            if (message_processor_thread_.joinable()) {
                message_processor_thread_.join();
            }
            
            // Stop heartbeat
            heartbeat_running_ = false;
            if (heartbeat_thread_.joinable()) {
                heartbeat_thread_.join();
            }
            
            // Stop worker threads
            io_context_.stop();
            for (auto& thread : worker_threads_) {
                if (thread.joinable()) {
                    thread.join();
                }
            }
            worker_threads_.clear();
            
            logger_->info("WebSocket server stopped successfully");
            return true;
            
        } catch (const std::exception& e) {
            logger_->error("Error stopping WebSocket server: {}", e.what());
            return false;
        }
    });
}

void WebSocketServer::run_worker() {
    try {
        io_context_.run();
    } catch (const std::exception& e) {
        logger_->error("WebSocket worker thread error: {}", e.what());
    }
}

void WebSocketServer::handle_accept() {
    if (!running_.load()) return;
    
    acceptor_->async_accept(
        [this](beast::error_code ec, tcp::socket socket) {
            if (!ec) {
                if (ws_config_.enable_ssl && ssl_context_) {
                    auto ssl_socket = std::make_unique<net::ssl::stream<tcp::socket>>(std::move(socket), *ssl_context_);
                    handle_ssl_connection(std::move(*ssl_socket));
                } else {
                    handle_connection(std::move(socket));
                }
            } else if (ec != net::error::operation_aborted) {
                logger_->error("WebSocket accept error: {}", ec.message());
            }
            
            // Continue accepting connections
            handle_accept();
        });
}

void WebSocketServer::handle_connection(tcp::socket socket) {
    try {
        // Check connection limit
        if (get_connection_count() >= ws_config_.max_connections) {
            logger_->warn("Connection limit reached, rejecting new connection");
            socket.close();
            return;
        }
        
        auto ws = std::make_unique<websocket::stream<tcp::socket>>(std::move(socket));
        
        // Set WebSocket options
        ws->set_option(websocket::stream_base::timeout::suggested(beast::role_type::server));
        ws->set_option(websocket::stream_base::decorator(
            [](websocket::response_type& res) {
                res.set(http::field::server, "QuantServices-SaaS-WebSocket/1.0");
            }));
        
        // Accept WebSocket handshake
        ws->async_accept(
            [this, ws = std::move(ws)](beast::error_code ec) mutable {
                if (!ec) {
                    std::string connection_id = generate_connection_id();
                    auto connection = std::make_shared<WebSocketConnection>(connection_id, std::move(ws));
                    add_connection(connection);
                    
                    // Start reading messages
                    // Implementation would continue with message reading loop
                    logger_->debug("WebSocket connection established: {}", connection_id);
                } else {
                    logger_->error("WebSocket handshake failed: {}", ec.message());
                }
            });
            
    } catch (const std::exception& e) {
        logger_->error("Error handling WebSocket connection: {}", e.what());
    }
}

std::string WebSocketServer::generate_connection_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    ss << std::hex;
    for (int i = 0; i < 16; ++i) {
        ss << dis(gen);
    }
    return ss.str();
}

void WebSocketServer::add_connection(std::shared_ptr<WebSocketConnection> connection) {
    std::unique_lock<std::shared_mutex> lock(connections_mutex_);
    connections_[connection->get_id()] = connection;
    active_connections_++;
    total_connections_++;
    
    logger_->debug("Added WebSocket connection: {}", connection->get_id());
}

void WebSocketServer::remove_connection(const std::string& connection_id) {
    std::unique_lock<std::shared_mutex> lock(connections_mutex_);
    
    auto it = connections_.find(connection_id);
    if (it != connections_.end()) {
        auto connection = it->second;
        
        // Remove from user connections
        if (!connection->get_user_id().empty()) {
            auto user_it = user_connections_.find(connection->get_user_id());
            if (user_it != user_connections_.end()) {
                user_it->second.erase(connection_id);
                if (user_it->second.empty()) {
                    user_connections_.erase(user_it);
                }
            }
        }
        
        // Remove from topic subscriptions
        for (const auto& topic : connection->get_subscriptions()) {
            auto topic_it = topic_subscribers_.find(topic);
            if (topic_it != topic_subscribers_.end()) {
                topic_it->second.erase(connection_id);
                if (topic_it->second.empty()) {
                    topic_subscribers_.erase(topic_it);
                }
            }
        }
        
        connections_.erase(it);
        active_connections_--;
        
        logger_->debug("Removed WebSocket connection: {}", connection_id);
    }
}

// Additional methods would be implemented here...
// For brevity, showing the key structure and patterns

bool WebSocketServer::is_running() const {
    return running_.load();
}

size_t WebSocketServer::get_connection_count() const {
    std::shared_lock<std::shared_mutex> lock(connections_mutex_);
    return connections_.size();
}

Json WebSocketServer::get_stats() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    
    auto now = std::chrono::steady_clock::now();
    auto uptime = std::chrono::duration_cast<std::chrono::seconds>(now - start_time_).count();
    
    return Json{
        {"uptime_seconds", uptime},
        {"total_connections", total_connections_.load()},
        {"active_connections", active_connections_.load()},
        {"messages_sent", messages_sent_.load()},
        {"messages_received", messages_received_.load()},
        {"max_connections", ws_config_.max_connections},
        {"is_running", running_.load()}
    };
}

void WebSocketServer::setup_market_data_subscriptions() {
    // This would integrate with QuantServicesProxy to receive market data updates
    // and forward them to subscribed WebSocket clients
}

} // namespace QuantServicesSaaS
