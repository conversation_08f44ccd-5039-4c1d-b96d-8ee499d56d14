/**
 * @file contexts/AuthContext.tsx
 * @brief Authentication context for managing user state
 * <AUTHOR> Team
 * @date 2024
 */

import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { User, LoginRequest, RegisterRequest, LoadingState } from '../types';
import { apiService } from '../services/api';
import { webSocketService } from '../services/websocket';

// Auth State
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  loadingState: LoadingState;
}

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: User }
  | { type: 'AUTH_FAILURE'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'AUTH_CLEAR_ERROR' }
  | { type: 'AUTH_UPDATE_USER'; payload: User };

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null,
  loadingState: 'idle',
};

// Auth reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
        loadingState: 'loading',
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        loadingState: 'success',
      };
    case 'AUTH_FAILURE':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
        loadingState: 'error',
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
        loadingState: 'idle',
      };
    case 'AUTH_CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    case 'AUTH_UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };
    default:
      return state;
  }
}

// Auth Context
interface AuthContextType extends AuthState {
  login: (credentials: LoginRequest) => Promise<boolean>;
  register: (userData: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<boolean>;
  changePassword: (oldPassword: string, newPassword: string) => Promise<boolean>;
  clearError: () => void;
  checkAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth Provider
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check authentication status on mount
  useEffect(() => {
    checkAuth();
  }, []);

  // Connect to WebSocket when authenticated
  useEffect(() => {
    if (state.isAuthenticated && state.user) {
      webSocketService.connect().catch((error) => {
        console.error('Failed to connect to WebSocket:', error);
      });
    } else {
      webSocketService.disconnect();
    }
  }, [state.isAuthenticated, state.user]);

  const checkAuth = async (): Promise<void> => {
    const token = localStorage.getItem('access_token');
    if (!token) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'No token found' });
      return;
    }

    dispatch({ type: 'AUTH_START' });

    try {
      const response = await apiService.getProfile();
      if (response.success && response.data) {
        dispatch({ type: 'AUTH_SUCCESS', payload: response.data });
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.error?.message || 'Authentication failed' });
      }
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'Authentication check failed' });
    }
  };

  const login = async (credentials: LoginRequest): Promise<boolean> => {
    dispatch({ type: 'AUTH_START' });

    try {
      const response = await apiService.login(credentials);
      if (response.success && response.data) {
        const { access_token, refresh_token, user } = response.data;
        apiService.setTokens(access_token, refresh_token);
        webSocketService.updateToken(access_token);
        dispatch({ type: 'AUTH_SUCCESS', payload: user });
        return true;
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.error?.message || 'Login failed' });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'Login request failed' });
      return false;
    }
  };

  const register = async (userData: RegisterRequest): Promise<boolean> => {
    dispatch({ type: 'AUTH_START' });

    try {
      const response = await apiService.register(userData);
      if (response.success && response.data) {
        // After successful registration, automatically log in
        const loginSuccess = await login({
          username: userData.username,
          password: userData.password,
        });
        return loginSuccess;
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.error?.message || 'Registration failed' });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'Registration request failed' });
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout request failed:', error);
    } finally {
      apiService.clearTokens();
      webSocketService.disconnect();
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<boolean> => {
    try {
      const response = await apiService.updateProfile(userData);
      if (response.success && response.data) {
        dispatch({ type: 'AUTH_UPDATE_USER', payload: response.data });
        return true;
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.error?.message || 'Profile update failed' });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'Profile update request failed' });
      return false;
    }
  };

  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      const response = await apiService.changePassword(oldPassword, newPassword);
      if (response.success) {
        return true;
      } else {
        dispatch({ type: 'AUTH_FAILURE', payload: response.error?.message || 'Password change failed' });
        return false;
      }
    } catch (error) {
      dispatch({ type: 'AUTH_FAILURE', payload: 'Password change request failed' });
      return false;
    }
  };

  const clearError = (): void => {
    dispatch({ type: 'AUTH_CLEAR_ERROR' });
  };

  const contextValue: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    updateProfile,
    changePassword,
    clearError,
    checkAuth,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
