/**
 * @file WebSocketServer.h
 * @brief WebSocket server for real-time data communication
 * <AUTHOR> Team
 * @date 2024
 */

#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>
#include <mutex>
#include <atomic>
#include <thread>
#include <queue>
#include <condition_variable>

#include <boost/beast.hpp>
#include <boost/asio.hpp>
#include <boost/asio/ssl.hpp>
#include <nlohmann/json.hpp>
#include <spdlog/spdlog.h>

namespace beast = boost::beast;
namespace http = beast::http;
namespace websocket = beast::websocket;
namespace net = boost::asio;
using tcp = net::ip::tcp;
using Json = nlohmann::json;

namespace QuantServicesSaaS {

// Forward declarations
class AuthenticationManager;
class QuantServicesProxy;

/**
 * @brief WebSocket connection wrapper
 */
class WebSocketConnection {
public:
    using WebSocketStream = websocket::stream<tcp::socket>;
    using SSLWebSocketStream = websocket::stream<net::ssl::stream<tcp::socket>>;

    WebSocketConnection(std::string id, std::unique_ptr<WebSocketStream> ws);
    WebSocketConnection(std::string id, std::unique_ptr<SSLWebSocketStream> ssl_ws);
    ~WebSocketConnection();

    const std::string& get_id() const { return id_; }
    const std::string& get_user_id() const { return user_id_; }
    void set_user_id(const std::string& user_id) { user_id_ = user_id; }
    
    bool is_authenticated() const { return authenticated_; }
    void set_authenticated(bool auth) { authenticated_ = auth; }
    
    const std::unordered_set<std::string>& get_subscriptions() const { return subscriptions_; }
    void add_subscription(const std::string& topic) { subscriptions_.insert(topic); }
    void remove_subscription(const std::string& topic) { subscriptions_.erase(topic); }
    void clear_subscriptions() { subscriptions_.clear(); }
    
    void send_message(const Json& message);
    void close();
    bool is_open() const;
    
    std::chrono::steady_clock::time_point last_activity() const { return last_activity_; }
    void update_activity() { last_activity_ = std::chrono::steady_clock::now(); }

private:
    std::string id_;
    std::string user_id_;
    bool authenticated_{false};
    std::unordered_set<std::string> subscriptions_;
    std::chrono::steady_clock::time_point last_activity_;
    
    std::unique_ptr<WebSocketStream> ws_;
    std::unique_ptr<SSLWebSocketStream> ssl_ws_;
    bool is_ssl_{false};
    
    mutable std::mutex send_mutex_;
    std::queue<Json> send_queue_;
    std::atomic<bool> sending_{false};
    
    void process_send_queue();
};

/**
 * @brief WebSocket message structure
 */
struct WebSocketMessage {
    std::string type;
    Json data;
    std::chrono::steady_clock::time_point timestamp;
    std::string target_user_id; // Empty for broadcast
    std::unordered_set<std::string> target_connection_ids; // Empty for all connections
};

/**
 * @brief WebSocket server configuration
 */
struct WebSocketConfig {
    std::string host{"0.0.0.0"};
    uint16_t port{9081};
    size_t worker_threads{4};
    std::chrono::seconds connection_timeout{300}; // 5 minutes
    std::chrono::seconds heartbeat_interval{30};
    size_t max_connections{1000};
    size_t message_queue_size{10000};
    bool enable_ssl{false};
    std::string ssl_cert_file;
    std::string ssl_key_file;
};

/**
 * @brief WebSocket Server class
 */
class WebSocketServer {
public:
    /**
     * @brief Constructor
     */
    explicit WebSocketServer(const Json& config,
                           std::shared_ptr<AuthenticationManager> auth_manager,
                           std::shared_ptr<QuantServicesProxy> qs_proxy);
    
    /**
     * @brief Destructor
     */
    ~WebSocketServer();
    
    /**
     * @brief Initialize server
     */
    bool initialize();
    
    /**
     * @brief Start server
     */
    std::future<bool> start();
    
    /**
     * @brief Stop server
     */
    std::future<bool> stop();
    
    /**
     * @brief Check if server is running
     */
    bool is_running() const;
    
    /**
     * @brief Broadcast message to all authenticated connections
     */
    void broadcast(const std::string& type, const Json& data);
    
    /**
     * @brief Send message to specific user
     */
    void send_to_user(const std::string& user_id, const std::string& type, const Json& data);
    
    /**
     * @brief Send message to specific connection
     */
    void send_to_connection(const std::string& connection_id, const std::string& type, const Json& data);
    
    /**
     * @brief Send message to subscribers of a topic
     */
    void send_to_subscribers(const std::string& topic, const std::string& type, const Json& data);
    
    /**
     * @brief Get server statistics
     */
    Json get_stats() const;
    
    /**
     * @brief Get connected users count
     */
    size_t get_connection_count() const;

private:
    // Configuration
    Json config_;
    WebSocketConfig ws_config_;
    
    // Core components
    std::shared_ptr<spdlog::logger> logger_;
    std::shared_ptr<AuthenticationManager> auth_manager_;
    std::shared_ptr<QuantServicesProxy> qs_proxy_;
    
    // Network components
    net::io_context io_context_;
    std::unique_ptr<tcp::acceptor> acceptor_;
    std::vector<std::thread> worker_threads_;
    
    // SSL context (if HTTPS enabled)
    std::unique_ptr<net::ssl::context> ssl_context_;
    
    // Connection management
    mutable std::shared_mutex connections_mutex_;
    std::unordered_map<std::string, std::shared_ptr<WebSocketConnection>> connections_;
    std::unordered_map<std::string, std::unordered_set<std::string>> user_connections_; // user_id -> connection_ids
    std::unordered_map<std::string, std::unordered_set<std::string>> topic_subscribers_; // topic -> connection_ids
    
    // Message queue
    std::mutex message_queue_mutex_;
    std::condition_variable message_queue_cv_;
    std::queue<WebSocketMessage> message_queue_;
    std::thread message_processor_thread_;
    std::atomic<bool> message_processing_{false};
    
    // Heartbeat and cleanup
    std::thread heartbeat_thread_;
    std::atomic<bool> heartbeat_running_{false};
    
    // Server state
    std::atomic<bool> running_{false};
    std::atomic<bool> initialized_{false};
    
    // Statistics
    mutable std::mutex stats_mutex_;
    std::atomic<uint64_t> total_connections_{0};
    std::atomic<uint64_t> active_connections_{0};
    std::atomic<uint64_t> messages_sent_{0};
    std::atomic<uint64_t> messages_received_{0};
    std::chrono::steady_clock::time_point start_time_;
    
    // Internal methods
    void load_config(const Json& config);
    void setup_ssl_context();
    
    void run_worker();
    void handle_accept();
    void handle_connection(tcp::socket socket);
    void handle_ssl_connection(net::ssl::stream<tcp::socket> ssl_socket);
    
    std::string generate_connection_id();
    void add_connection(std::shared_ptr<WebSocketConnection> connection);
    void remove_connection(const std::string& connection_id);
    
    void process_message_queue();
    void send_heartbeat();
    void cleanup_inactive_connections();
    
    // Message handlers
    void handle_client_message(std::shared_ptr<WebSocketConnection> connection, const Json& message);
    void handle_authentication(std::shared_ptr<WebSocketConnection> connection, const Json& data);
    void handle_subscription(std::shared_ptr<WebSocketConnection> connection, const Json& data);
    void handle_unsubscription(std::shared_ptr<WebSocketConnection> connection, const Json& data);
    void handle_ping(std::shared_ptr<WebSocketConnection> connection, const Json& data);
    
    // Market data integration
    void setup_market_data_subscriptions();
    void handle_market_data_update(const Json& data);
    void handle_trading_update(const Json& data);
    void handle_system_update(const Json& data);
    
    // Utility methods
    Json create_message(const std::string& type, const Json& data);
    void queue_message(const WebSocketMessage& message);
    void update_stats(bool message_sent, bool message_received = false);
};

} // namespace QuantServicesSaaS
