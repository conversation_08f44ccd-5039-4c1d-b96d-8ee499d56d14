/**
 * @file services/websocket.ts
 * @brief WebSocket service for real-time data communication
 * <AUTHOR> Team
 * @date 2024
 */

import { io, Socket } from 'socket.io-client';
import {
  WebSocketMessage,
  MarketDataUpdate,
  TradingUpdate,
  SystemUpdate,
  Quote,
  Order,
  Position,
  Portfolio
} from '../types';

// WebSocket Configuration
const WS_URL = import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:9081';
const RECONNECT_ATTEMPTS = 5;
const RECONNECT_DELAY = 1000; // 1 second

export type WebSocketEventHandler = (data: any) => void;

export interface WebSocketSubscription {
  event: string;
  handler: WebSocketEventHandler;
}

class WebSocketService {
  private socket: Socket | null = null;
  private subscriptions: Map<string, Set<WebSocketEventHandler>> = new Map();
  private isConnected = false;
  private reconnectAttempts = 0;
  private accessToken: string | null = null;

  constructor() {
    this.loadAccessToken();
  }

  private loadAccessToken(): void {
    this.accessToken = localStorage.getItem('access_token');
  }

  // Connection management
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.loadAccessToken();

      this.socket = io(WS_URL, {
        auth: {
          token: this.accessToken,
        },
        transports: ['websocket'],
        reconnection: true,
        reconnectionAttempts: RECONNECT_ATTEMPTS,
        reconnectionDelay: RECONNECT_DELAY,
      });

      this.socket.on('connect', () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connection', { status: 'connected' });
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        this.isConnected = false;
        this.emit('connection', { status: 'disconnected', reason });
      });

      this.socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        this.isConnected = false;
        this.emit('connection', { status: 'error', error: error.message });
        
        if (this.reconnectAttempts >= RECONNECT_ATTEMPTS) {
          reject(new Error('Failed to connect to WebSocket server'));
        }
        this.reconnectAttempts++;
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log('WebSocket reconnected after', attemptNumber, 'attempts');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.emit('connection', { status: 'reconnected', attempts: attemptNumber });
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('WebSocket reconnection error:', error);
        this.emit('connection', { status: 'reconnect_error', error: error.message });
      });

      this.socket.on('reconnect_failed', () => {
        console.error('WebSocket reconnection failed');
        this.isConnected = false;
        this.emit('connection', { status: 'reconnect_failed' });
      });

      // Handle authentication errors
      this.socket.on('auth_error', (error) => {
        console.error('WebSocket authentication error:', error);
        this.emit('auth_error', error);
        this.disconnect();
      });

      // Set up message handlers
      this.setupMessageHandlers();
    });
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnected = false;
    this.subscriptions.clear();
  }

  private setupMessageHandlers(): void {
    if (!this.socket) return;

    // Market data updates
    this.socket.on('market_data', (data: MarketDataUpdate['data']) => {
      this.emit('market_data', data);
      
      if (data.quotes) {
        data.quotes.forEach((quote: Quote) => {
          this.emit(`quote:${quote.symbol}`, quote);
        });
      }
    });

    // Trading updates
    this.socket.on('trading', (data: TradingUpdate['data']) => {
      this.emit('trading', data);
      
      if (data.orders) {
        this.emit('orders', data.orders);
      }
      
      if (data.positions) {
        this.emit('positions', data.positions);
      }
      
      if (data.portfolio) {
        this.emit('portfolio', data.portfolio);
      }
    });

    // System updates
    this.socket.on('system', (data: SystemUpdate['data']) => {
      this.emit('system', data);
    });

    // Error handling
    this.socket.on('error', (error: any) => {
      console.error('WebSocket error:', error);
      this.emit('error', error);
    });
  }

  // Subscription management
  subscribe(event: string, handler: WebSocketEventHandler): () => void {
    if (!this.subscriptions.has(event)) {
      this.subscriptions.set(event, new Set());
    }
    
    this.subscriptions.get(event)!.add(handler);

    // Return unsubscribe function
    return () => {
      this.unsubscribe(event, handler);
    };
  }

  unsubscribe(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.subscriptions.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.subscriptions.delete(event);
      }
    }
  }

  private emit(event: string, data: any): void {
    const handlers = this.subscriptions.get(event);
    if (handlers) {
      handlers.forEach((handler) => {
        try {
          handler(data);
        } catch (error) {
          console.error('Error in WebSocket event handler:', error);
        }
      });
    }
  }

  // Market data subscriptions
  subscribeToQuotes(symbols: string[]): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_quotes', { symbols });
    }
  }

  unsubscribeFromQuotes(symbols: string[]): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_quotes', { symbols });
    }
  }

  subscribeToTrades(symbols: string[]): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_trades', { symbols });
    }
  }

  unsubscribeFromTrades(symbols: string[]): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_trades', { symbols });
    }
  }

  // Trading subscriptions
  subscribeToOrders(): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_orders');
    }
  }

  unsubscribeFromOrders(): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_orders');
    }
  }

  subscribeToPositions(): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_positions');
    }
  }

  unsubscribeFromPositions(): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_positions');
    }
  }

  subscribeToPortfolio(): void {
    if (this.socket?.connected) {
      this.socket.emit('subscribe_portfolio');
    }
  }

  unsubscribeFromPortfolio(): void {
    if (this.socket?.connected) {
      this.socket.emit('unsubscribe_portfolio');
    }
  }

  // Utility methods
  isConnectedToServer(): boolean {
    return this.isConnected && this.socket?.connected === true;
  }

  getConnectionStatus(): string {
    if (!this.socket) return 'disconnected';
    if (this.socket.connected) return 'connected';
    if (this.socket.connecting) return 'connecting';
    return 'disconnected';
  }

  // Send custom messages
  send(event: string, data: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket not connected, cannot send message:', event);
    }
  }

  // Update authentication token
  updateToken(token: string): void {
    this.accessToken = token;
    if (this.socket?.connected) {
      this.socket.auth = { token };
      this.socket.disconnect();
      this.socket.connect();
    }
  }
}

// Export singleton instance
export const webSocketService = new WebSocketService();
export default webSocketService;
