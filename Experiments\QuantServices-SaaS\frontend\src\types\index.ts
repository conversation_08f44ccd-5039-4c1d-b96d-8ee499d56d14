/**
 * @file types/index.ts
 * @brief TypeScript type definitions for QuantServices SaaS Frontend
 * <AUTHOR> Team
 * @date 2024
 */

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    details?: string;
    code: number;
  };
  timestamp: number;
}

// Authentication Types
export interface User {
  user_id: string;
  username: string;
  email: string;
  role: string;
  permissions: string[];
  is_active: boolean;
  email_verified: boolean;
  created_at: number;
  last_login: number;
  metadata?: Record<string, any>;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  role?: string;
}

export interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: User;
}

export interface SessionInfo {
  session_id: string;
  user_id: string;
  created_at: number;
  expires_at: number;
  last_activity: number;
  remote_address: string;
  user_agent: string;
  is_active: boolean;
}

// Market Data Types
export interface Quote {
  symbol: string;
  price: number;
  bid: number;
  ask: number;
  volume: number;
  timestamp: number;
  change?: number;
  change_percent?: number;
  high?: number;
  low?: number;
  open?: number;
  close?: number;
}

export interface Bar {
  symbol: string;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  interval: string;
}

export interface Tick {
  symbol: string;
  timestamp: number;
  price: number;
  volume: number;
  side: 'buy' | 'sell';
}

export interface SymbolInfo {
  symbol: string;
  name: string;
  exchange: string;
  currency: string;
  type: 'stock' | 'future' | 'option' | 'forex' | 'crypto';
  sector?: string;
  industry?: string;
  market_cap?: number;
  description?: string;
  is_active: boolean;
}

export interface MarketStatus {
  market: string;
  status: 'open' | 'closed' | 'pre_market' | 'after_hours';
  next_open?: number;
  next_close?: number;
  timezone: string;
}

// Trading Types
export interface Order {
  order_id: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stop_limit';
  quantity: number;
  price?: number;
  stop_price?: number;
  status: 'pending' | 'filled' | 'partially_filled' | 'cancelled' | 'rejected';
  filled_quantity: number;
  average_price?: number;
  created_at: number;
  updated_at: number;
  user_id: string;
  strategy_id?: string;
}

export interface Position {
  symbol: string;
  quantity: number;
  average_price: number;
  market_value: number;
  unrealized_pnl: number;
  realized_pnl: number;
  side: 'long' | 'short';
  created_at: number;
  updated_at: number;
}

export interface Portfolio {
  total_value: number;
  cash: number;
  equity: number;
  buying_power: number;
  day_pnl: number;
  total_pnl: number;
  positions: Position[];
  currency: string;
  updated_at: number;
}

export interface PortfolioPerformance {
  period: string;
  start_date: number;
  end_date: number;
  total_return: number;
  total_return_percent: number;
  annualized_return: number;
  volatility: number;
  sharpe_ratio: number;
  max_drawdown: number;
  win_rate: number;
  profit_factor: number;
  daily_returns: Array<{
    date: number;
    return: number;
    cumulative_return: number;
  }>;
}

// Strategy Types
export interface Strategy {
  strategy_id: string;
  name: string;
  description: string;
  type: 'algorithmic' | 'manual' | 'copy_trading';
  status: 'active' | 'inactive' | 'paused' | 'error';
  parameters: Record<string, any>;
  symbols: string[];
  created_at: number;
  updated_at: number;
  user_id: string;
  performance?: StrategyPerformance;
}

export interface StrategyPerformance {
  total_trades: number;
  winning_trades: number;
  losing_trades: number;
  win_rate: number;
  total_pnl: number;
  average_win: number;
  average_loss: number;
  profit_factor: number;
  max_drawdown: number;
  sharpe_ratio: number;
  start_date: number;
  end_date: number;
}

// WebSocket Types
export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface MarketDataUpdate extends WebSocketMessage {
  type: 'market_data';
  data: {
    quotes?: Quote[];
    bars?: Bar[];
    ticks?: Tick[];
  };
}

export interface TradingUpdate extends WebSocketMessage {
  type: 'trading';
  data: {
    orders?: Order[];
    positions?: Position[];
    portfolio?: Portfolio;
  };
}

export interface SystemUpdate extends WebSocketMessage {
  type: 'system';
  data: {
    status?: string;
    message?: string;
    level?: 'info' | 'warning' | 'error';
  };
}

// UI State Types
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
}

export interface MarketDataState {
  quotes: Record<string, Quote>;
  subscribedSymbols: string[];
  isConnected: boolean;
  lastUpdate: number;
}

export interface TradingState {
  orders: Order[];
  positions: Position[];
  portfolio: Portfolio | null;
  isConnected: boolean;
  lastUpdate: number;
}

// Chart Types
export interface ChartData {
  timestamp: number;
  value: number;
  label?: string;
}

export interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

// Configuration Types
export interface AppConfig {
  apiBaseUrl: string;
  websocketUrl: string;
  features: {
    trading: boolean;
    backtesting: boolean;
    analytics: boolean;
    notifications: boolean;
    darkMode: boolean;
  };
  defaults: {
    refreshInterval: number;
    chartInterval: string;
    maxHistoryDays: number;
  };
}

// Utility Types
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface PaginationParams {
  page: number;
  limit: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface FilterParams {
  search?: string;
  dateFrom?: string;
  dateTo?: string;
  status?: string;
  type?: string;
}

export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
  sortable?: boolean;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: string;
  timestamp: number;
}

export type ErrorSeverity = 'error' | 'warning' | 'info' | 'success';
