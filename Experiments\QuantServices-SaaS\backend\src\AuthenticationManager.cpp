/**
 * @file AuthenticationManager.cpp
 * @brief Authentication management implementation
 * <AUTHOR> Team
 * @date 2024
 */

#include "AuthenticationManager.h"
#include <random>
#include <iomanip>
#include <sstream>
#include <regex>
#include <openssl/sha.h>
#include <openssl/rand.h>

namespace QuantServicesSaaS {

AuthenticationManager::AuthenticationManager(const Json& config)
    : config_(config)
    , logger_(spdlog::get("saas-backend"))
    , jwt_algorithm_(jwt::algorithm::hs256{""}) {
    
    if (!logger_) {
        logger_ = spdlog::default_logger();
    }
    
    load_config(config);
}

AuthenticationManager::~AuthenticationManager() = default;

void AuthenticationManager::load_config(const Json& config) {
    auto auth_config = config.value("authentication", Json::object());
    
    auth_config_.secret_key = auth_config.value("secret_key", "default-secret-key-change-in-production");
    auth_config_.token_expiry = std::chrono::hours(auth_config.value("token_expiry_hours", 24));
    auth_config_.refresh_token_expiry = std::chrono::hours(24 * auth_config.value("refresh_token_expiry_days", 30));
    auth_config_.password_min_length = auth_config.value("password_min_length", 8);
    auth_config_.enable_2fa = auth_config.value("enable_2fa", false);
    auth_config_.session_timeout = std::chrono::minutes(auth_config.value("session_timeout_minutes", 60));
    auth_config_.max_sessions_per_user = auth_config.value("max_sessions_per_user", 5);
    auth_config_.enable_password_complexity = auth_config.value("enable_password_complexity", true);
    
    // Initialize JWT algorithm with secret
    jwt_secret_ = auth_config_.secret_key;
    jwt_algorithm_ = jwt::algorithm::hs256{jwt_secret_};
}

bool AuthenticationManager::initialize() {
    try {
        logger_->info("Initializing Authentication Manager...");
        
        // Validate configuration
        if (jwt_secret_.empty() || jwt_secret_ == "default-secret-key-change-in-production") {
            logger_->warn("Using default JWT secret key - change this in production!");
        }
        
        if (jwt_secret_.length() < 32) {
            logger_->warn("JWT secret key is shorter than recommended (32 characters)");
        }
        
        // Create default admin user if none exists
        if (users_.empty()) {
            UserInfo admin_user;
            admin_user.user_id = generate_user_id();
            admin_user.username = "admin";
            admin_user.email = "<EMAIL>";
            admin_user.password_hash = hash_password("admin123");
            admin_user.role = "admin";
            admin_user.permissions = {"*"}; // All permissions
            admin_user.is_active = true;
            admin_user.email_verified = true;
            admin_user.created_at = std::chrono::system_clock::now();
            admin_user.last_login = std::chrono::system_clock::now();
            
            users_[admin_user.user_id] = admin_user;
            username_to_id_[admin_user.username] = admin_user.user_id;
            
            logger_->info("Created default admin user: admin/admin123");
        }
        
        logger_->info("Authentication Manager initialized successfully");
        return true;
        
    } catch (const std::exception& e) {
        logger_->error("Failed to initialize Authentication Manager: {}", e.what());
        return false;
    }
}

AuthResult AuthenticationManager::register_user(const std::string& username, const std::string& email,
                                               const std::string& password, const std::string& role) {
    try {
        // Validate input
        if (username.empty() || email.empty() || password.empty()) {
            return {false, "Username, email, and password are required"};
        }
        
        if (username.length() < 3 || username.length() > 32) {
            return {false, "Username must be between 3 and 32 characters"};
        }
        
        if (password.length() < auth_config_.password_min_length) {
            return {false, "Password must be at least " + std::to_string(auth_config_.password_min_length) + " characters"};
        }
        
        if (auth_config_.enable_password_complexity && !validate_password_strength(password)) {
            return {false, "Password must contain uppercase, lowercase, number, and special character"};
        }
        
        // Validate email format
        std::regex email_regex(R"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
        if (!std::regex_match(email, email_regex)) {
            return {false, "Invalid email format"};
        }
        
        // Check if username already exists
        {
            std::shared_lock<std::shared_mutex> lock(users_mutex_);
            if (username_to_id_.find(username) != username_to_id_.end()) {
                return {false, "Username already exists"};
            }
            
            // Check if email already exists
            for (const auto& [id, user] : users_) {
                if (user.email == email) {
                    return {false, "Email already registered"};
                }
            }
        }
        
        // Create new user
        UserInfo new_user;
        new_user.user_id = generate_user_id();
        new_user.username = username;
        new_user.email = email;
        new_user.password_hash = hash_password(password);
        new_user.role = role;
        new_user.is_active = true;
        new_user.email_verified = false;
        new_user.created_at = std::chrono::system_clock::now();
        new_user.last_login = std::chrono::system_clock::now();
        
        // Set default permissions based on role
        if (role == "admin") {
            new_user.permissions = {"*"};
        } else if (role == "trader") {
            new_user.permissions = {
                "market_data:read", "trading:read", "trading:write",
                "portfolio:read", "portfolio:write", "strategies:read", "strategies:write"
            };
        } else if (role == "viewer") {
            new_user.permissions = {
                "market_data:read", "trading:read", "portfolio:read", "strategies:read"
            };
        } else {
            new_user.permissions = {"market_data:read", "portfolio:read"};
        }
        
        // Store user
        {
            std::unique_lock<std::shared_mutex> lock(users_mutex_);
            users_[new_user.user_id] = new_user;
            username_to_id_[new_user.username] = new_user.user_id;
        }
        
        total_registrations_++;
        
        logger_->info("User registered successfully: {} ({})", username, new_user.user_id);
        
        AuthResult result;
        result.success = true;
        result.message = "User registered successfully";
        result.user = new_user;
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Error in register_user: {}", e.what());
        return {false, "Registration failed due to internal error"};
    }
}

AuthResult AuthenticationManager::authenticate(const std::string& username, const std::string& password,
                                             const std::string& remote_address, const std::string& user_agent) {
    try {
        update_login_stats(false); // Will be updated to true if successful
        
        // Find user by username
        std::optional<UserInfo> user_opt;
        {
            std::shared_lock<std::shared_mutex> lock(users_mutex_);
            auto it = username_to_id_.find(username);
            if (it != username_to_id_.end()) {
                auto user_it = users_.find(it->second);
                if (user_it != users_.end()) {
                    user_opt = user_it->second;
                }
            }
        }
        
        if (!user_opt) {
            logger_->warn("Authentication failed: user not found: {}", username);
            return {false, "Invalid username or password"};
        }
        
        UserInfo& user = user_opt.value();
        
        // Check if user is active
        if (!user.is_active) {
            logger_->warn("Authentication failed: user inactive: {}", username);
            return {false, "Account is disabled"};
        }
        
        // Verify password
        if (!verify_password(password, user.password_hash)) {
            logger_->warn("Authentication failed: invalid password for user: {}", username);
            return {false, "Invalid username or password"};
        }
        
        // Check session limit
        cleanup_user_sessions(user.user_id);
        {
            std::shared_lock<std::shared_mutex> lock(sessions_mutex_);
            auto it = user_sessions_.find(user.user_id);
            if (it != user_sessions_.end() && it->second.size() >= auth_config_.max_sessions_per_user) {
                logger_->warn("Authentication failed: session limit reached for user: {}", username);
                return {false, "Maximum number of sessions reached"};
            }
        }
        
        // Create new session
        SessionInfo session;
        session.session_id = generate_session_id();
        session.user_id = user.user_id;
        session.created_at = std::chrono::system_clock::now();
        session.expires_at = session.created_at + auth_config_.session_timeout;
        session.last_activity = session.created_at;
        session.remote_address = remote_address;
        session.user_agent = user_agent;
        session.is_active = true;
        
        // Generate tokens
        session.access_token = generate_access_token(user, session.session_id);
        session.refresh_token = generate_refresh_token(user.user_id, session.session_id);
        
        // Store session
        {
            std::unique_lock<std::shared_mutex> lock(sessions_mutex_);
            sessions_[session.session_id] = session;
            user_sessions_[user.user_id].push_back(session.session_id);
        }
        
        // Update user last login
        {
            std::unique_lock<std::shared_mutex> lock(users_mutex_);
            users_[user.user_id].last_login = std::chrono::system_clock::now();
        }
        
        active_sessions_++;
        update_login_stats(true);
        
        logger_->info("User authenticated successfully: {} ({})", username, user.user_id);
        
        AuthResult result;
        result.success = true;
        result.message = "Authentication successful";
        result.user = user;
        result.session = session;
        result.access_token = session.access_token;
        result.refresh_token = session.refresh_token;
        
        return result;
        
    } catch (const std::exception& e) {
        logger_->error("Error in authenticate: {}", e.what());
        return {false, "Authentication failed due to internal error"};
    }
}

TokenValidationResult AuthenticationManager::validate_token(const std::string& token) {
    try {
        auto decoded = decode_token(token);
        
        // Extract claims
        std::string user_id = decoded.get_payload_claim("user_id").as_string();
        std::string session_id = decoded.get_payload_claim("session_id").as_string();
        auto expires_at = std::chrono::system_clock::from_time_t(decoded.get_expires_at());
        
        // Check if token is expired
        if (std::chrono::system_clock::now() > expires_at) {
            return {false, "", "", {}, expires_at, "Token expired"};
        }
        
        // Check if session exists and is active
        std::optional<SessionInfo> session_opt;
        {
            std::shared_lock<std::shared_mutex> lock(sessions_mutex_);
            auto it = sessions_.find(session_id);
            if (it != sessions_.end()) {
                session_opt = it->second;
            }
        }
        
        if (!session_opt || !session_opt->is_active) {
            return {false, "", "", {}, expires_at, "Session not found or inactive"};
        }
        
        // Check if session is expired
        if (is_session_expired(session_opt.value())) {
            return {false, "", "", {}, expires_at, "Session expired"};
        }
        
        // Get user permissions
        std::vector<std::string> permissions;
        {
            std::shared_lock<std::shared_mutex> lock(users_mutex_);
            auto it = users_.find(user_id);
            if (it != users_.end()) {
                permissions = it->second.permissions;
            }
        }
        
        return {true, user_id, session_id, permissions, expires_at, ""};
        
    } catch (const std::exception& e) {
        return {false, "", "", {}, std::chrono::system_clock::now(), "Invalid token: " + std::string(e.what())};
    }
}

// Helper methods implementation
std::string AuthenticationManager::generate_user_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    ss << "user_" << std::hex;
    for (int i = 0; i < 16; ++i) {
        ss << dis(gen);
    }
    return ss.str();
}

std::string AuthenticationManager::generate_session_id() {
    static std::random_device rd;
    static std::mt19937 gen(rd());
    static std::uniform_int_distribution<> dis(0, 15);
    
    std::stringstream ss;
    ss << "sess_" << std::hex;
    for (int i = 0; i < 24; ++i) {
        ss << dis(gen);
    }
    return ss.str();
}

std::string AuthenticationManager::hash_password(const std::string& password) {
    // Simple SHA-256 hash for demonstration
    // In production, use bcrypt or similar with salt
    unsigned char hash[SHA256_DIGEST_LENGTH];
    SHA256_CTX sha256;
    SHA256_Init(&sha256);
    SHA256_Update(&sha256, password.c_str(), password.length());
    SHA256_Final(hash, &sha256);
    
    std::stringstream ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)hash[i];
    }
    return ss.str();
}

bool AuthenticationManager::verify_password(const std::string& password, const std::string& hash) {
    return hash_password(password) == hash;
}

bool AuthenticationManager::validate_password_strength(const std::string& password) {
    // Check for uppercase, lowercase, number, and special character
    bool has_upper = false, has_lower = false, has_digit = false, has_special = false;
    
    for (char c : password) {
        if (std::isupper(c)) has_upper = true;
        else if (std::islower(c)) has_lower = true;
        else if (std::isdigit(c)) has_digit = true;
        else if (std::ispunct(c)) has_special = true;
    }
    
    return has_upper && has_lower && has_digit && has_special;
}

std::string AuthenticationManager::generate_access_token(const UserInfo& user, const std::string& session_id) {
    auto now = std::chrono::system_clock::now();
    auto expires_at = now + auth_config_.token_expiry;
    
    return jwt::create()
        .set_issuer("quantservices-saas")
        .set_type("JWT")
        .set_issued_at(now)
        .set_expires_at(expires_at)
        .set_payload_claim("user_id", jwt::claim(user.user_id))
        .set_payload_claim("username", jwt::claim(user.username))
        .set_payload_claim("role", jwt::claim(user.role))
        .set_payload_claim("session_id", jwt::claim(session_id))
        .sign(jwt_algorithm_);
}

std::string AuthenticationManager::generate_refresh_token(const std::string& user_id, const std::string& session_id) {
    auto now = std::chrono::system_clock::now();
    auto expires_at = now + auth_config_.refresh_token_expiry;
    
    return jwt::create()
        .set_issuer("quantservices-saas")
        .set_type("refresh")
        .set_issued_at(now)
        .set_expires_at(expires_at)
        .set_payload_claim("user_id", jwt::claim(user_id))
        .set_payload_claim("session_id", jwt::claim(session_id))
        .sign(jwt_algorithm_);
}

jwt::decoded_jwt<jwt::traits::kazuho_picojson> AuthenticationManager::decode_token(const std::string& token) {
    auto verifier = jwt::verify()
        .allow_algorithm(jwt_algorithm_)
        .with_issuer("quantservices-saas");
    
    return verifier.verify(jwt::decode(token));
}

void AuthenticationManager::update_login_stats(bool success) {
    total_logins_++;
    if (success) {
        successful_logins_++;
    } else {
        failed_logins_++;
    }
}

// Additional methods would be implemented here...

} // namespace QuantServicesSaaS
