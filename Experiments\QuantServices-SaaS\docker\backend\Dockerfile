# Multi-stage build for QuantServices SaaS Backend
FROM ubuntu:22.04 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    pkg-config \
    libssl-dev \
    libboost-all-dev \
    libsqlite3-dev \
    && rm -rf /var/lib/apt/lists/*

# Install modern C++ compiler
RUN apt-get update && apt-get install -y \
    gcc-11 \
    g++-11 \
    && update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-11 60 \
    && update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-11 60 \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /build

# Copy source code
COPY . .

# Create build directory and configure
RUN mkdir -p build && cd build && \
    cmake .. \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_CXX_STANDARD=20 \
    -DCMAKE_INSTALL_PREFIX=/app

# Build the application
RUN cd build && make -j$(nproc)

# Install the application
RUN cd build && make install

# Runtime stage
FROM ubuntu:22.04 AS runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libssl3 \
    libboost-system1.74.0 \
    libboost-filesystem1.74.0 \
    libboost-thread1.74.0 \
    libsqlite3-0 \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create application user
RUN groupadd -r quantservices && useradd -r -g quantservices quantservices

# Set working directory
WORKDIR /app

# Copy application from builder stage
COPY --from=builder /app /app

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/config && \
    chown -R quantservices:quantservices /app

# Copy configuration files
COPY config/ /app/config/

# Switch to application user
USER quantservices

# Expose ports
EXPOSE 9080 9081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:9080/api/v1/system/health || exit 1

# Set environment variables
ENV CONFIG_FILE=/app/config/saas-config.json
ENV LOG_LEVEL=info

# Start the application
CMD ["/app/bin/QuantServices-SaaS-Backend"]
