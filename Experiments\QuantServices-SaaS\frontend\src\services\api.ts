/**
 * @file services/api.ts
 * @brief API service for communicating with QuantServices SaaS Backend
 * <AUTHOR> Team
 * @date 2024
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  ApiResponse,
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  User,
  Quote,
  Bar,
  Order,
  Position,
  Portfolio,
  Strategy,
  SymbolInfo,
  MarketStatus,
  PortfolioPerformance,
  SessionInfo
} from '../types';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:9080/api/v1';
const REQUEST_TIMEOUT = 30000; // 30 seconds

class ApiService {
  private client: AxiosInstance;
  private accessToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: REQUEST_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle errors
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired, try to refresh
          const refreshToken = localStorage.getItem('refresh_token');
          if (refreshToken) {
            try {
              const response = await this.refreshToken(refreshToken);
              this.setTokens(response.access_token, response.refresh_token);
              
              // Retry the original request
              const originalRequest = error.config;
              originalRequest.headers.Authorization = `Bearer ${response.access_token}`;
              return this.client.request(originalRequest);
            } catch (refreshError) {
              // Refresh failed, redirect to login
              this.clearTokens();
              window.location.href = '/login';
            }
          } else {
            // No refresh token, redirect to login
            this.clearTokens();
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );

    // Load tokens from localStorage
    this.loadTokens();
  }

  // Token management
  setTokens(accessToken: string, refreshToken: string): void {
    this.accessToken = accessToken;
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  clearTokens(): void {
    this.accessToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  private loadTokens(): void {
    this.accessToken = localStorage.getItem('access_token');
  }

  // Generic request method
  private async request<T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client.request(config);
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        return error.response.data;
      }
      return {
        success: false,
        error: {
          message: error.message || 'Network error',
          code: error.response?.status || 0,
        },
        timestamp: Date.now(),
      };
    }
  }

  // Authentication API
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return this.request<AuthResponse>({
      method: 'POST',
      url: '/auth/login',
      data: credentials,
    });
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<User>> {
    return this.request<User>({
      method: 'POST',
      url: '/auth/register',
      data: userData,
    });
  }

  async logout(): Promise<ApiResponse<void>> {
    const response = await this.request<void>({
      method: 'POST',
      url: '/auth/logout',
    });
    this.clearTokens();
    return response;
  }

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await this.client.post<ApiResponse<AuthResponse>>('/auth/refresh', {
      refresh_token: refreshToken,
    });
    
    if (response.data.success && response.data.data) {
      return response.data.data;
    }
    throw new Error('Failed to refresh token');
  }

  async getProfile(): Promise<ApiResponse<User>> {
    return this.request<User>({
      method: 'GET',
      url: '/auth/profile',
    });
  }

  async updateProfile(userData: Partial<User>): Promise<ApiResponse<User>> {
    return this.request<User>({
      method: 'PUT',
      url: '/auth/profile',
      data: userData,
    });
  }

  async changePassword(oldPassword: string, newPassword: string): Promise<ApiResponse<void>> {
    return this.request<void>({
      method: 'POST',
      url: '/auth/change-password',
      data: { old_password: oldPassword, new_password: newPassword },
    });
  }

  async getSessions(): Promise<ApiResponse<SessionInfo[]>> {
    return this.request<SessionInfo[]>({
      method: 'GET',
      url: '/auth/sessions',
    });
  }

  // Market Data API
  async getQuote(symbol: string): Promise<ApiResponse<Quote>> {
    return this.request<Quote>({
      method: 'GET',
      url: `/market-data/quote/${symbol}`,
    });
  }

  async getQuotes(symbols: string[]): Promise<ApiResponse<{ quotes: Quote[]; count: number }>> {
    return this.request<{ quotes: Quote[]; count: number }>({
      method: 'GET',
      url: '/market-data/quotes',
      params: { symbols: symbols.join(',') },
    });
  }

  async getLatestQuotes(): Promise<ApiResponse<{ quotes: Quote[]; count: number }>> {
    return this.request<{ quotes: Quote[]; count: number }>({
      method: 'GET',
      url: '/market-data/quotes/latest',
    });
  }

  async getBars(
    symbol: string,
    params: {
      start_date?: string;
      end_date?: string;
      interval?: string;
      limit?: number;
    }
  ): Promise<ApiResponse<{ bars: Bar[]; count: number }>> {
    return this.request<{ bars: Bar[]; count: number }>({
      method: 'GET',
      url: `/market-data/bars/${symbol}`,
      params,
    });
  }

  async getSymbols(): Promise<ApiResponse<SymbolInfo[]>> {
    return this.request<SymbolInfo[]>({
      method: 'GET',
      url: '/market-data/symbols',
    });
  }

  async getSymbolInfo(symbol: string): Promise<ApiResponse<SymbolInfo>> {
    return this.request<SymbolInfo>({
      method: 'GET',
      url: `/market-data/symbols/${symbol}/info`,
    });
  }

  async getMarketStatus(): Promise<ApiResponse<MarketStatus[]>> {
    return this.request<MarketStatus[]>({
      method: 'GET',
      url: '/market-data/market-status',
    });
  }

  // Trading API
  async getOrders(params?: {
    status?: string;
    symbol?: string;
    limit?: number;
  }): Promise<ApiResponse<{ orders: Order[]; count: number }>> {
    return this.request<{ orders: Order[]; count: number }>({
      method: 'GET',
      url: '/trading/orders',
      params,
    });
  }

  async getOrder(orderId: string): Promise<ApiResponse<Order>> {
    return this.request<Order>({
      method: 'GET',
      url: `/trading/orders/${orderId}`,
    });
  }

  async placeOrder(order: {
    symbol: string;
    side: 'buy' | 'sell';
    type: 'market' | 'limit' | 'stop' | 'stop_limit';
    quantity: number;
    price?: number;
    stop_price?: number;
  }): Promise<ApiResponse<Order>> {
    return this.request<Order>({
      method: 'POST',
      url: '/trading/orders',
      data: order,
    });
  }

  async cancelOrder(orderId: string): Promise<ApiResponse<void>> {
    return this.request<void>({
      method: 'DELETE',
      url: `/trading/orders/${orderId}`,
    });
  }

  async getPositions(): Promise<ApiResponse<{ positions: Position[]; count: number }>> {
    return this.request<{ positions: Position[]; count: number }>({
      method: 'GET',
      url: '/trading/positions',
    });
  }

  async getPosition(symbol: string): Promise<ApiResponse<Position>> {
    return this.request<Position>({
      method: 'GET',
      url: `/trading/positions/${symbol}`,
    });
  }

  // Portfolio API
  async getPortfolio(): Promise<ApiResponse<Portfolio>> {
    return this.request<Portfolio>({
      method: 'GET',
      url: '/portfolio',
    });
  }

  async getPortfolioPerformance(params: {
    period?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<ApiResponse<PortfolioPerformance>> {
    return this.request<PortfolioPerformance>({
      method: 'GET',
      url: '/portfolio/performance',
      params,
    });
  }

  // Strategy API
  async getStrategies(): Promise<ApiResponse<{ strategies: Strategy[]; count: number }>> {
    return this.request<{ strategies: Strategy[]; count: number }>({
      method: 'GET',
      url: '/strategies',
    });
  }

  async getStrategy(strategyId: string): Promise<ApiResponse<Strategy>> {
    return this.request<Strategy>({
      method: 'GET',
      url: `/strategies/${strategyId}`,
    });
  }

  async createStrategy(strategy: {
    name: string;
    description: string;
    type: string;
    parameters: Record<string, any>;
    symbols: string[];
  }): Promise<ApiResponse<Strategy>> {
    return this.request<Strategy>({
      method: 'POST',
      url: '/strategies',
      data: strategy,
    });
  }

  async updateStrategy(strategyId: string, strategy: Partial<Strategy>): Promise<ApiResponse<Strategy>> {
    return this.request<Strategy>({
      method: 'PUT',
      url: `/strategies/${strategyId}`,
      data: strategy,
    });
  }

  async deleteStrategy(strategyId: string): Promise<ApiResponse<void>> {
    return this.request<void>({
      method: 'DELETE',
      url: `/strategies/${strategyId}`,
    });
  }

  async startStrategy(strategyId: string): Promise<ApiResponse<void>> {
    return this.request<void>({
      method: 'POST',
      url: `/strategies/${strategyId}/start`,
    });
  }

  async stopStrategy(strategyId: string): Promise<ApiResponse<void>> {
    return this.request<void>({
      method: 'POST',
      url: `/strategies/${strategyId}/stop`,
    });
  }

  // System API
  async getSystemStatus(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: '/system/status',
    });
  }

  async getSystemMetrics(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: '/system/metrics',
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;
