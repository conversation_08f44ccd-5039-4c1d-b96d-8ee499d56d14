version: '3.8'

services:
  # Backend C++ Service
  backend:
    build:
      context: ../backend
      dockerfile: ../docker/backend/Dockerfile
    container_name: quantservices-saas-backend
    ports:
      - "9080:9080"  # HTTP API
      - "9081:9081"  # WebSocket
    environment:
      - CONFIG_FILE=/app/config/saas-config.json
      - LOG_LEVEL=info
    volumes:
      - ../shared/config:/app/config:ro
      - backend_logs:/app/logs
      - backend_data:/app/data
    depends_on:
      - quantservices
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9080/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend React Application
  frontend:
    build:
      context: ../frontend
      dockerfile: ../docker/frontend/Dockerfile
      args:
        - VITE_API_BASE_URL=http://localhost:9080/api/v1
        - VITE_WEBSOCKET_URL=ws://localhost:9081
    container_name: quantservices-saas-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # QuantServices Backend (existing service)
  quantservices:
    build:
      context: ../../QuantServices
      dockerfile: ../docker/quantservices/Dockerfile
    container_name: quantservices-backend
    ports:
      - "8080:8080"
    environment:
      - CONFIG_FILE=/app/config/default.json
      - LOG_LEVEL=info
    volumes:
      - ../../QuantServices/config:/app/config:ro
      - quantservices_logs:/app/logs
      - quantservices_data:/app/data
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quantservices-saas-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ../docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ../docker/nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching (Optional)
  redis:
    image: redis:7-alpine
    container_name: quantservices-saas-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database (Optional)
  postgres:
    image: postgres:15-alpine
    container_name: quantservices-saas-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=quantservices_saas
      - POSTGRES_USER=quantservices
      - POSTGRES_PASSWORD=quantservices_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - quantservices-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quantservices -d quantservices_saas"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus (Optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: quantservices-saas-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ../docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - quantservices-network
    restart: unless-stopped

  # Grafana for Visualization (Optional)
  grafana:
    image: grafana/grafana:latest
    container_name: quantservices-saas-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ../docker/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - quantservices-network
    restart: unless-stopped

networks:
  quantservices-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  backend_logs:
    driver: local
  backend_data:
    driver: local
  quantservices_logs:
    driver: local
  quantservices_data:
    driver: local
  nginx_logs:
    driver: local
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
